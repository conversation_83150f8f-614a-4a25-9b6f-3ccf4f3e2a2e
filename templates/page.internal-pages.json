/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "blocks": {
        "text_A8F3Wr": {
          "type": "text",
          "name": "t:names.heading",
          "settings": {
            "text": "<p>{{ closest.page.title }}</p>",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "h1",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        },
        "text_KMwtTr": {
          "type": "text",
          "name": "t:names.text",
          "settings": {
            "text": "{{ closest.page.content }}",
            "width": "fit-content",
            "max_width": "normal",
            "alignment": "left",
            "type_preset": "rte",
            "font": "var(--font-body--family)",
            "font_size": "1rem",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "var(--color-foreground)",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0,
            "custom_css_class": ""
          },
          "blocks": {}
        }
      },
      "block_order": [
        "text_A8F3Wr",
        "text_KMwtTr"
      ],
      "disabled": true,
      "settings": {
        "content_direction": "column",
        "gap": 32,
        "color_scheme": "",
        "padding-block-start": 40,
        "padding-block-end": 80
      }
    },
    "internal_page_layout_section_FTrXwJ": {
      "type": "internal-page-layout-section",
      "blocks": {
        "link_wMYGwX": {
          "type": "link",
          "settings": {
            "link_label": "כללי",
            "link": ""
          }
        },
        "link_3aiMPt": {
          "type": "link",
          "settings": {
            "link_label": "רכישת מוצרים",
            "link": ""
          }
        },
        "link_bYdnaF": {
          "type": "link",
          "settings": {
            "link_label": "תשלום",
            "link": ""
          }
        },
        "link_6Abnzn": {
          "type": "link",
          "settings": {
            "link_label": "זיכויים, ביטולים והחזרות",
            "link": ""
          }
        },
        "link_tLxBzC": {
          "type": "link",
          "settings": {
            "link_label": "הרשמה לאתר והרשמה למועדון לקוחות",
            "link": ""
          }
        },
        "link_LXXftH": {
          "type": "link",
          "settings": {
            "link_label": "שימוש במידע",
            "link": ""
          }
        },
        "link_BbrQki": {
          "type": "link",
          "settings": {
            "link_label": "זכויות היוצרים",
            "link": ""
          }
        },
        "link_m9F9bV": {
          "type": "link",
          "settings": {
            "link_label": "תקנון משלוחים",
            "link": ""
          }
        }
      },
      "block_order": [
        "link_wMYGwX",
        "link_3aiMPt",
        "link_bYdnaF",
        "link_6Abnzn",
        "link_tLxBzC",
        "link_LXXftH",
        "link_BbrQki",
        "link_m9F9bV"
      ],
      "name": "Internal Page Layout",
      "settings": {
        "override_title": "{{ page.title }}",
        "updated_text": "עדכון אחרון: 12/01/2025",
        "sidebar_title": ""
      }
    }
  },
  "order": [
    "main",
    "internal_page_layout_section_FTrXwJ"
  ]
}
